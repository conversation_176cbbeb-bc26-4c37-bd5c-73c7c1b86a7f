{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755766167884}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,sBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC,cAFA;MAGAG;IAHA,CATA;IAcAC;MACAL,aADA;MAEAI;IAFA;EAdA,CAFA;;EAqBAE;IACA;MACAC,WADA;MAEAC,WAFA;MAGAC,eAHA,CAIA;;IAJA;EAMA,CA5BA;;EA6BAC;IACA;IACA;EACA,CAhCA;;EAiCAC;IACA;IACA;EACA,CApCA;;EAqCAC;IACAC;MACA;QACA;QACA;UACA;UACA;YACAb,cADA;YAEAc,IAFA;YAGAC,IAHA;YAIAC,KAJA;YAKAC,KALA;YAMAC,aACA;cAAAC;cAAAC;YAAA,CADA,EAEA;cAAAD;cAAAC;YAAA,CAFA;UANA;QAWA,CAbA,MAaA;UACA;UACA;YACApB,cADA;YAEAc,IAFA;YAGAC,IAHA;YAIAC,KAJA;YAKAC,KALA;YAMAC,aACA;cAAAC;cAAAC;YAAA,CADA,EAEA;cAAAD;cAAAC;YAAA,CAFA;UANA;QAWA;MACA,CA7BA,MA6BA;QACA;QACA;UACApB,cADA;UAEAc,IAFA;UAGAC,IAHA;UAIAC,KAJA;UAKAC,KALA;UAMAC,aACA;YAAAC;YAAAC;UAAA,CADA,EAEA;YAAAD;YAAAC;UAAA,CAFA;QANA;MAWA;IACA,CA7CA;;IA8CAC;MACA;IACA,CAhDA;;IAiDAC;MACA;QACAC;UACAC,yFADA;UAEAC,SAFA;UAGAC,OAHA;UAIAC,UAJA;UAKAC;QALA,CADA;QAQAC;UACA7B,aADA;UAEA8B,MAFA;UAGAC,wDAHA;UAIAC;YAAAC;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAE;YAAAF;UAAA,CANA;UAOAG;YAAAH;UAAA;QAPA,CARA;QAiBAI,QACA;UACArC,gBADA;UAEAsC,aAFA;UAGAhC,iCAHA;UAIA6B;YAAAF;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAG;YACAH,UADA;YAEAM,cAFA;YAGAC,UAHA;YAIAC;cACA;gBACA;cACA,CAFA,MAEA;gBACA;gBACA;cACA;YACA,CAXA;YAYAC;cACAC;gBACAvB,iCADA;gBAEAwB,YAFA;gBAGAC,iBAHA;gBAIAC,iBAJA;gBAKAP,aALA;gBAMA;gBACAQ;cAPA,CADA;cAUAlD;gBACAuB,aADA;gBAEAwB,YAFA;gBAGAG;cAHA;YAVA;UAZA;QANA,CADA,EAqCA;UACA;UACA/C,gBAFA;UAGAsC,aAHA;UAIAhC,kCAJA;UAKA6B;YAAAF;UAAA,CALA;UAMAC;YAAAD;UAAA,CANA;UAOAG;YACAH,UADA;YAEAb,gBAFA;YAGAwB,YAHA;YAIAL,aAJA;YAKAC;UALA;QAPA,CArCA,CAjBA;QAsEAQ,SACA;UACAhD,WADA;UAEAiD,WAFA;UAGAC,aAHA;UAIA5C;YACA6C,iBADA;YAEAC;cACAhC,6EADA;cAEAiC;YAFA;UAFA,GAJA;UAWAC;QAXA,CADA,EAcA;UACA;UACAtD,WAFA;UAGAiD,WAHA;UAIAC,aAJA;UAKA5C,yEALA;UAMA8C;YACAhC,2BADA;YAEAiC;UAFA,CANA;UAUAE,eAVA;UAWAD;QAXA,CAdA;MAtEA;IAmGA,CArJA;;IAsJAE;MACA;MACA;MACA;MACA;MACAC;IACA,CA5JA;;IA6JAC;MACA;MACA;;MACA;QACApD;MACA,CAFA,MAEA;QACA;QACA;QACA;;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAdA,CAeA;;;MACA;IACA,CA9KA;;IA+KAqD;MACA;MACA;QACA;QACA;QACA;MACA,CAJA,EAIA,IAJA;IAKA,CAtLA;;IAuLAC;MACA;IACA;;EAzLA;AArCA", "names": ["name", "props", "id", "type", "required", "showCount", "chartData", "default", "alternateColors", "data", "chart", "timer", "currentIndex", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getBarColor", "x", "y", "x2", "y2", "colorStops", "offset", "color", "getBgBarColor", "getOption", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "min", "max", "splitLine", "show", "axisLine", "axisTick", "axisLabel", "yAxis", "inverse", "align", "margin", "formatter", "rich", "num", "fontSize", "fontFamily", "fontWeight", "padding", "series", "<PERSON><PERSON><PERSON><PERSON>", "yAxisIndex", "value", "itemStyle", "borderRadius", "z", "barGap", "initChart", "window", "<PERSON><PERSON><PERSON>", "startScroll", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["BarScrollChart.vue"], "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    },\r\n    alternateColors: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor (index = 0) {\r\n      if (this.alternateColors) {\r\n        // 交替颜色：蓝色和黄色\r\n        if (index % 2 === 0) {\r\n          // 蓝色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#062553' },\r\n              { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n            ]\r\n          }\r\n        } else {\r\n          // 黄色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#072756' },\r\n              { offset: 1, color: 'rgba(245, 231, 79, 1)' }\r\n            ]\r\n          }\r\n        }\r\n      } else {\r\n        // 默认蓝色渐变\r\n        return {\r\n          type: 'linear',\r\n          x: 0,\r\n          y: 0,\r\n          x2: 1,\r\n          y2: 0,\r\n          colorStops: [\r\n            { offset: 0, color: '#062553' },\r\n            { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 15 : this.id === 'activityTypeChart' ? 45 : 35,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              align: 'right',\r\n              margin: 16,\r\n              formatter: (value, idx) => {\r\n                if (this.id === 'activityTypeChart') {\r\n                  return `{name|${value}}`\r\n                } else {\r\n                  const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n                  return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`\r\n                }\r\n              },\r\n              rich: {\r\n                num: {\r\n                  color: 'rgba(255, 255, 255, 0.5)',\r\n                  fontSize: 13,\r\n                  fontFamily: 'DIN',\r\n                  fontWeight: '500',\r\n                  align: 'left',\r\n                  // width: 25,\r\n                  padding: [4, 0, 0, 0]\r\n                },\r\n                name: {\r\n                  color: '#fff',\r\n                  fontSize: 15,\r\n                  padding: [0, 0, 0, 4]\r\n                }\r\n              }\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 0,\r\n            data: data.map((item, index) => ({\r\n              value: item.value,\r\n              itemStyle: {\r\n                color: this.getBarColor(this.alternateColors ? (this.currentIndex + index) : 0),\r\n                borderRadius: 6\r\n              }\r\n            })),\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 0,\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}