{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue", "mtime": 1755768107392}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA+GA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,eADA;MAEA;MACAC,kBACA;QAAAL;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAXA,EAYA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAZA,EAaA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAbA,EAcA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAdA,EAeA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAfA,EAgBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAhBA,EAiBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAjBA,EAkBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAlBA,EAmBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAnBA,EAoBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CApBA,EAqBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CArBA,EAsBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAtBA,EAuBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAvBA,EAwBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAxBA,EAyBA;QAAAX;QAAAM;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CAzBA,CAHA;MA8BA;MACAC,mBACA;QAAAZ;QAAAa;MAAA,CADA,EAEA;QAAAb;QAAAa;MAAA,CAFA,EAGA;QAAAb;QAAAa;MAAA,CAHA,EAIA;QAAAb;QAAAa;MAAA,CAJA,EAKA;QAAAb;QAAAa;MAAA,CALA,EAMA;QAAAb;QAAAa;MAAA,CANA,EAOA;QAAAb;QAAAa;MAAA,CAPA,EAQA;QAAAb;QAAAa;MAAA,CARA,EASA;QAAAb;QAAAa;MAAA,CATA,EAUA;QAAAb;QAAAa;MAAA,CAVA,EAWA;QAAAb;QAAAa;MAAA,CAXA,EAYA;QAAAb;QAAAa;MAAA,CAZA,EAaA;QAAAb;QAAAa;MAAA,CAbA,EAcA;QAAAb;QAAAa;MAAA,CAdA,EAeA;QAAAb;QAAAa;MAAA,CAfA,EAgBA;QAAAb;QAAAa;MAAA,CAhBA,EAiBA;QAAAb;QAAAa;MAAA,CAjBA,CA/BA;MAkDA;MACAC,kBACA;QAAAd;QAAAa;MAAA,CADA,EAEA;QAAAb;QAAAa;MAAA,CAFA,EAGA;QAAAb;QAAAa;MAAA,CAHA,EAIA;QAAAb;QAAAa;MAAA,CAJA,CAnDA;MAyDA;MACAE,yBACA;QAAAf;QAAAa;QAAAG;MAAA,CADA,EAEA;QAAAhB;QAAAa;QAAAG;MAAA,CAFA,EAGA;QAAAhB;QAAAa;QAAAG;MAAA,CAHA,EAIA;QAAAhB;QAAAa;QAAAG;MAAA,CAJA,EAKA;QAAAhB;QAAAa;QAAAG;MAAA,CALA,EAMA;QAAAhB;QAAAa;QAAAG;MAAA,CANA;IA1DA;EAmEA,CAzEA;;EA0EAC,YA1EA;;EA4EAC;IACA;IACA;IACA;EACA,CAhFA;;EAiFAC;IACA;MACAC;IACA;EACA,CArFA;;EAsFAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA,CApBA;;IAqBA;IACAC;MACA;MACA;MACA;MACA,6BAJA,CAKA;;MACAC;IACA;;EA7BA;AAtFA", "names": ["name", "components", "BarScrollChart", "data", "currentTime", "performanceData", "meeting", "proposal", "opinion", "suggestion", "reading", "training", "activityTypeData", "value", "meetingTypeData", "performanceSummaryData", "icon", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path", "handleNodeClick", "console"], "sourceRoot": "src/views/smartBrainLargeScreen/performanceStatistics", "sources": ["performanceStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>履职统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 年度履职汇总 -->\r\n        <div class=\"performance-summary-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年度履职汇总</span>\r\n          </div>\r\n          <div class=\"performance-summary-content\">\r\n            <div class=\"summary-grid\">\r\n              <div class=\"summary-item\" v-for=\"(item, index) in performanceSummaryData\" :key=\"index\">\r\n                <div class=\"summary-icon\">\r\n                  <img :src=\"item.icon\" :alt=\"item.name\" />\r\n                </div>\r\n                <div class=\"summary-info\">\r\n                  <div class=\"summary-label\">{{ item.name }}</div>\r\n                  <div class=\"summary-value\">{{ item.value }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 会议类型统计 -->\r\n        <div class=\"metting-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议类型统计</span>\r\n          </div>\r\n          <div class=\"metting-content\">\r\n            <div class=\"meeting-grid\">\r\n              <div class=\"meeting-item\" v-for=\"(item, index) in meetingTypeData\" :key=\"index\">\r\n                <div class=\"meeting-number\">{{ item.value }}</div>\r\n                <div class=\"meeting-label\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 活动类型统计 -->\r\n        <div class=\"activity-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">活动类型统计</span>\r\n          </div>\r\n          <div class=\"activity-content\">\r\n            <BarScrollChart id=\"activityTypeChart\" :showCount=\"12\" :chart-data=\"activityTypeData\"\r\n              :alternate-colors=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 履职数据分析 -->\r\n        <div class=\"performance-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职数据分析</span>\r\n          </div>\r\n          <div class=\"performance-content\">\r\n            <div class=\"table-container\">\r\n              <!-- 固定表头 -->\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell\">姓名</div>\r\n                <div class=\"header-cell\">会议活动</div>\r\n                <div class=\"header-cell\">政协提案</div>\r\n                <div class=\"header-cell\">社情民意</div>\r\n                <div class=\"header-cell\">议政建言</div>\r\n                <div class=\"header-cell\">读书心得</div>\r\n                <div class=\"header-cell\">委员培训</div>\r\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\r\n              </div>\r\n              <!-- 可滚动内容 -->\r\n              <div class=\"table-body\">\r\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\r\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\r\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\r\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\r\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\r\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\r\n                  </div>\r\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\r\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 履职数据分析表格数据\r\n      performanceData: [\r\n        { name: '马家', meeting: 15, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '马家', meeting: 0, proposal: 12, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '主席团', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },\r\n        { name: '主席团', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 60, training: 0 },\r\n        { name: '主席', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '刘振南', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '刘军', meeting: 20, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '吴学宁', meeting: 15, proposal: 38, opinion: 0, suggestion: 0, reading: 55, training: 0 },\r\n        { name: '杨文军', meeting: 60, proposal: 28, opinion: 0, suggestion: 0, reading: 55, training: 0 },\r\n        { name: '苏勇', meeting: 9, proposal: 13, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },\r\n        { name: '领作', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 60, training: 0 },\r\n        { name: '海勇员', meeting: 20, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '周明', meeting: 15, proposal: 38, opinion: 0, suggestion: 0, reading: 55, training: 0 },\r\n        { name: '刘峰', meeting: 15, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '吴芳梅', meeting: 0, proposal: 12, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '朱敏', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },\r\n        { name: '苏勇', meeting: 9, proposal: 13, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 }\r\n      ],\r\n      // 活动类型统计数据\r\n      activityTypeData: [\r\n        { name: '视察', value: 32 },\r\n        { name: '调研', value: 20 },\r\n        { name: '协商', value: 14 },\r\n        { name: '学习培训', value: 22 },\r\n        { name: '联系界别群众', value: 8 },\r\n        { name: '提案督查', value: 26 },\r\n        { name: '提案督办', value: 13 },\r\n        { name: '提案评议', value: 32 },\r\n        { name: '委员联络小组', value: 15 },\r\n        { name: '委员会客厅', value: 25 },\r\n        { name: '联系社会组织', value: 10 },\r\n        { name: '界别群众重点关切问题情况通报会', value: 20 },\r\n        { name: '社情民意座谈会', value: 16 },\r\n        { name: '接受媒体采访', value: 28 },\r\n        { name: '经济协作参加各委会议活动', value: 5 },\r\n        { name: '宣讲党的政策', value: 7 },\r\n        { name: '服务为民', value: 32 }\r\n      ],\r\n      // 会议类型统计数据\r\n      meetingTypeData: [\r\n        { name: '全体会议', value: 2 },\r\n        { name: '常委会议', value: 10 },\r\n        { name: '主席会议', value: 12 },\r\n        { name: '其他会议', value: 28 }\r\n      ],\r\n      // 年度履职汇总数据\r\n      performanceSummaryData: [\r\n        { name: '提交提案', value: 1500, icon: require('../../../assets/largeScreen/icon_submit_proposal.png') },\r\n        { name: '提交社情民意', value: 1057, icon: require('../../../assets/largeScreen/icon_submit_social.png') },\r\n        { name: '网络议政', value: 215, icon: require('../../../assets/largeScreen/icon_network.png') },\r\n        { name: '参加会议', value: 361, icon: require('../../../assets/largeScreen/icon_metting_item.png') },\r\n        { name: '参加活动', value: 104, icon: require('../../../assets/largeScreen/icon_activity_item.png') },\r\n        { name: '其他履职', value: 241, icon: require('../../../assets/largeScreen/item_other_duties.png') }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 922px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 年度履职汇总\r\n      .performance-summary-section {\r\n        height: 390px;\r\n        background: url('../../../assets/largeScreen/icon_performance_summary_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .performance-summary-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n\r\n          .summary-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr 1fr;\r\n            gap: 12px;\r\n            height: 100%;\r\n\r\n            .summary-item {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .summary-icon {\r\n                width: 64px;\r\n                height: 64px;\r\n                margin-right: 12px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border-radius: 6px;\r\n\r\n                img {\r\n                  width: 64px;\r\n                  height: 64px;\r\n                  object-fit: contain;\r\n                }\r\n              }\r\n\r\n              .summary-info {\r\n                flex: 1;\r\n\r\n                .summary-label {\r\n                  font-weight: 400;\r\n                  font-size: 15px;\r\n                  color: #B4C0CC;\r\n                  margin-bottom: 8px;\r\n                  line-height: 20px;\r\n                }\r\n\r\n                .summary-value {\r\n                  font-family: DIN, DIN;\r\n                  font-weight: 500;\r\n                  font-size: 32px;\r\n                  color: #FFFFFF;\r\n                  line-height: 30px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 会议类型统计\r\n      .metting-section {\r\n        height: 390px;\r\n        background: url('../../../assets/largeScreen/icon_metting_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .metting-content {\r\n          margin-top: 60px;\r\n          height: calc(100% - 70px);\r\n\r\n          .meeting-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr;\r\n            gap: 15px;\r\n            height: 100%;\r\n\r\n            .meeting-item {\r\n              width: 200px;\r\n              height: 125px;\r\n              background: url('../../../assets/largeScreen/icon_metting_type.png') no-repeat;\r\n              background-size: 100% 100%;\r\n              background-position: center;\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n              padding: 15px;\r\n              cursor: pointer;\r\n\r\n              .meeting-number {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 10px;\r\n                font-family: 'DIN', Arial, sans-serif;\r\n                transition: all 0.3s ease;\r\n              }\r\n\r\n              .meeting-label {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                font-weight: 500;\r\n                text-align: center;\r\n                line-height: 1.2;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 活动类型统计\r\n      .activity-section {\r\n        height: 550px;\r\n        background: url('../../../assets/largeScreen/icon_activity_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1 / -1; // 跨越两列（活动类型统计在第二行）\r\n        grid-row: 2; // 第二行\r\n\r\n        .activity-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 履职数据分析\r\n      .performance-analysis-section {\r\n        background: url('../../../assets/largeScreen/icon_performance_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .performance-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n\r\n          .table-container {\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            border: 1px solid #117090;\r\n            overflow: hidden;\r\n            --name-col-width: 124px;\r\n            --scrollbar-width: 6px;\r\n\r\n            .table-header {\r\n              display: grid;\r\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\r\n              border-bottom: 1px solid #117090;\r\n              position: sticky;\r\n              top: 0;\r\n              z-index: 10;\r\n\r\n              .header-cell {\r\n                height: 40px;\r\n                line-height: 40px;\r\n                text-align: center;\r\n                font-weight: 400;\r\n                color: #B4C0CC;\r\n                font-size: 15px;\r\n                border-right: 1px solid #117090;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &:last-child {\r\n                  border-right: none;\r\n                  background: transparent;\r\n                  border: none;\r\n                }\r\n\r\n                // &.name-col {\r\n                //   background: rgba(0, 100, 180, 0.9);\r\n                //   font-weight: 600;\r\n                // }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 6px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(0, 30, 60, 0.3);\r\n                border-radius: 3px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(0, 212, 255, 0.4);\r\n                border-radius: 3px;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.6);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: grid;\r\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\r\n                border-bottom: 1px solid #117090;\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.1);\r\n                }\r\n\r\n                .table-cell {\r\n                  padding: 12px 8px;\r\n                  text-align: center;\r\n                  color: #FFFFFF;\r\n                  font-size: 14px;\r\n                  border-right: 1px solid #117090;\r\n                  transition: all 0.3s ease;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  background: rgba(31, 198, 255, 0.16);\r\n\r\n                  &:last-child {\r\n                    border-right: none;\r\n                  }\r\n\r\n                  &.name-col {\r\n                    background: rgba(31, 198, 255, 0.16);\r\n                    color: #FFF;\r\n                    font-weight: 500;\r\n                  }\r\n\r\n                  &.meeting-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    color: #59F7CA;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                  }\r\n\r\n                  &.proposal-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #00FFF7;\r\n                  }\r\n\r\n                  &.opinion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF386B;\r\n                  }\r\n\r\n                  &.suggestion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #81C4E4;\r\n                  }\r\n\r\n                  &.reading-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #387BFD;\r\n                  }\r\n\r\n                  &.training-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF911F;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}