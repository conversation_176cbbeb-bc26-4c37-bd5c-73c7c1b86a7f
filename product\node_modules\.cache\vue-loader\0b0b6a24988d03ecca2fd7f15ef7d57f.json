{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue", "mtime": 1755763896758}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["performanceStatisticsBox.vue"], "names": [], "mappings": ";AAkEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "performanceStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/performanceStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>履职统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 年度履职汇总 -->\r\n        <div class=\"performance-summary-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年度履职汇总</span>\r\n          </div>\r\n          <div class=\"performance-summary-content\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 会议类型统计 -->\r\n        <div class=\"metting-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议类型统计</span>\r\n          </div>\r\n          <div class=\"metting-content\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 活动类型统计 -->\r\n        <div class=\"activity-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">活动类型统计</span>\r\n          </div>\r\n          <div class=\"activity-content\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 履职数据分析 -->\r\n        <div class=\"performance-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职数据分析</span>\r\n          </div>\r\n          <div class=\"performance-content\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      selectedArea: '青岛',\r\n      selectedDistrictCode: '',\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'name'\r\n      },\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比',\r\n      memberTotalNum: 418,\r\n      standingMemberTotalNum: 42\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 922px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 年度履职汇总\r\n      .performance-summary-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .performance-summary-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n        }\r\n      }\r\n\r\n      // 会议类型统计\r\n      .metting-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .metting-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n\r\n      // 活动类型统计\r\n      .activity-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1 / -1; // 跨越两列（活动类型统计在第二行）\r\n        grid-row: 2; // 第二行\r\n\r\n        .activity-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 履职数据分析\r\n      .performance-analysis-section {\r\n        background: url('../../../assets/largeScreen/icon_performance_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .performance-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}