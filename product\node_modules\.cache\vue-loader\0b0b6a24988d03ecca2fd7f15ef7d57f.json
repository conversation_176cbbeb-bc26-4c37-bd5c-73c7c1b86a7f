{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue", "mtime": 1755766174782}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["performanceStatisticsBox.vue"], "names": [], "mappings": ";AA8FA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "performanceStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/performanceStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>履职统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 年度履职汇总 -->\r\n        <div class=\"performance-summary-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年度履职汇总</span>\r\n          </div>\r\n          <div class=\"performance-summary-content\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 会议类型统计 -->\r\n        <div class=\"metting-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议类型统计</span>\r\n          </div>\r\n          <div class=\"metting-content\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 活动类型统计 -->\r\n        <div class=\"activity-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">活动类型统计</span>\r\n          </div>\r\n          <div class=\"activity-content\">\r\n            <BarScrollChart id=\"activityTypeChart\" :showCount=\"10\" :chart-data=\"activityTypeData\"\r\n              :alternate-colors=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 履职数据分析 -->\r\n        <div class=\"performance-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职数据分析</span>\r\n          </div>\r\n          <div class=\"performance-content\">\r\n            <div class=\"table-container\">\r\n              <!-- 固定表头 -->\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell\">姓名</div>\r\n                <div class=\"header-cell\">会议活动</div>\r\n                <div class=\"header-cell\">政协提案</div>\r\n                <div class=\"header-cell\">社情民意</div>\r\n                <div class=\"header-cell\">议政建言</div>\r\n                <div class=\"header-cell\">读书心得</div>\r\n                <div class=\"header-cell\">委员培训</div>\r\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\r\n              </div>\r\n              <!-- 可滚动内容 -->\r\n              <div class=\"table-body\">\r\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\r\n                  <div class=\"table-cell name-col\">{{ item.name }}</div>\r\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\r\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\r\n                  <div class=\"table-cell opinion-col\">{{ item.opinion }}</div>\r\n                  <div class=\"table-cell suggestion-col\">{{ item.suggestion }}\r\n                  </div>\r\n                  <div class=\"table-cell reading-col\">{{ item.reading }}</div>\r\n                  <div class=\"table-cell training-col\">{{ item.training }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 履职数据分析表格数据\r\n      performanceData: [\r\n        { name: '马家', meeting: 15, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '马家', meeting: 0, proposal: 12, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '主席团', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },\r\n        { name: '主席团', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 60, training: 0 },\r\n        { name: '主席', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '刘振南', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '刘军', meeting: 20, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '吴学宁', meeting: 15, proposal: 38, opinion: 0, suggestion: 0, reading: 55, training: 0 },\r\n        { name: '杨文军', meeting: 60, proposal: 28, opinion: 0, suggestion: 0, reading: 55, training: 0 },\r\n        { name: '苏勇', meeting: 9, proposal: 13, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },\r\n        { name: '领作', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 60, training: 0 },\r\n        { name: '海勇员', meeting: 20, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '周明', meeting: 15, proposal: 38, opinion: 0, suggestion: 0, reading: 55, training: 0 },\r\n        { name: '刘峰', meeting: 15, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '吴芳梅', meeting: 0, proposal: 12, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '朱敏', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },\r\n        { name: '苏勇', meeting: 9, proposal: 13, opinion: 0, suggestion: 0, reading: 0, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },\r\n        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },\r\n        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 }\r\n      ],\r\n      // 活动类型统计数据\r\n      activityTypeData: [\r\n        { name: '视察', value: 32 },\r\n        { name: '调研', value: 20 },\r\n        { name: '协商', value: 14 },\r\n        { name: '学习培训', value: 22 },\r\n        { name: '联系界别群众', value: 8 },\r\n        { name: '提案督查', value: 26 },\r\n        { name: '提案督办', value: 13 },\r\n        { name: '提案评议', value: 32 },\r\n        { name: '委员联络小组', value: 15 },\r\n        { name: '委员会客厅', value: 25 },\r\n        { name: '联系社会组织', value: 10 },\r\n        { name: '界别群众重点关切问题情况通报会', value: 20 },\r\n        { name: '社情民意座谈会', value: 16 },\r\n        { name: '接受媒体采访', value: 28 },\r\n        { name: '经济协作参加各委会议活动', value: 5 },\r\n        { name: '宣讲党的政策', value: 7 },\r\n        { name: '服务为民', value: 32 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 922px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 年度履职汇总\r\n      .performance-summary-section {\r\n        background: url('../../../assets/largeScreen/icon_performance_summary_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .performance-summary-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n        }\r\n      }\r\n\r\n      // 会议类型统计\r\n      .metting-section {\r\n        background: url('../../../assets/largeScreen/icon_metting_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .metting-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n\r\n      // 活动类型统计\r\n      .activity-section {\r\n        background: url('../../../assets/largeScreen/icon_activity_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1 / -1; // 跨越两列（活动类型统计在第二行）\r\n        grid-row: 2; // 第二行\r\n\r\n        .activity-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 履职数据分析\r\n      .performance-analysis-section {\r\n        background: url('../../../assets/largeScreen/icon_performance_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .performance-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n\r\n          .table-container {\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            border: 1px solid #117090;\r\n            overflow: hidden;\r\n            --name-col-width: 124px;\r\n            --scrollbar-width: 6px;\r\n\r\n            .table-header {\r\n              display: grid;\r\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\r\n              border-bottom: 1px solid #117090;\r\n              position: sticky;\r\n              top: 0;\r\n              z-index: 10;\r\n\r\n              .header-cell {\r\n                height: 40px;\r\n                line-height: 40px;\r\n                text-align: center;\r\n                font-weight: 400;\r\n                color: #B4C0CC;\r\n                font-size: 15px;\r\n                border-right: 1px solid #117090;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &:last-child {\r\n                  border-right: none;\r\n                  background: transparent;\r\n                  border: none;\r\n                }\r\n\r\n                // &.name-col {\r\n                //   background: rgba(0, 100, 180, 0.9);\r\n                //   font-weight: 600;\r\n                // }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 6px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(0, 30, 60, 0.3);\r\n                border-radius: 3px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(0, 212, 255, 0.4);\r\n                border-radius: 3px;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.6);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: grid;\r\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\r\n                border-bottom: 1px solid #117090;\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.1);\r\n                }\r\n\r\n                .table-cell {\r\n                  padding: 12px 8px;\r\n                  text-align: center;\r\n                  color: #FFFFFF;\r\n                  font-size: 14px;\r\n                  border-right: 1px solid #117090;\r\n                  transition: all 0.3s ease;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  background: rgba(31, 198, 255, 0.16);\r\n\r\n                  &:last-child {\r\n                    border-right: none;\r\n                  }\r\n\r\n                  &.name-col {\r\n                    background: rgba(31, 198, 255, 0.16);\r\n                    color: #FFF;\r\n                    font-weight: 500;\r\n                  }\r\n\r\n                  &.meeting-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    color: #59F7CA;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                  }\r\n\r\n                  &.proposal-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #00FFF7;\r\n                  }\r\n\r\n                  &.opinion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF386B;\r\n                  }\r\n\r\n                  &.suggestion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #81C4E4;\r\n                  }\r\n\r\n                  &.reading-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #387BFD;\r\n                  }\r\n\r\n                  &.training-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF911F;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}