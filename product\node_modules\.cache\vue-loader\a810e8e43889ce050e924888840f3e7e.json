{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue?vue&type=template&id=4beb7f23&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue", "mtime": 1755768928257}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "_v", "_s", "currentTime", "_m", "on", "click", "goHome", "_l", "performanceSummaryData", "item", "index", "key", "attrs", "src", "icon", "alt", "name", "value", "meetingTypeData", "id", "showCount", "activityTypeData", "performanceData", "meeting", "proposal", "opinion", "suggestion", "reading", "training", "staticRenderFns", "staticStyle", "height", "require", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/views/smartBrainLargeScreen/performanceStatistics/performanceStatisticsBox.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"bigScreen\", staticClass: \"big-screen\" }, [\n    _c(\"div\", { staticClass: \"screen-header\" }, [\n      _c(\"div\", { staticClass: \"header-left\" }, [\n        _c(\"span\", { staticClass: \"date-time\" }, [\n          _vm._v(_vm._s(_vm.currentTime)),\n        ]),\n        _c(\"span\", { staticClass: \"weather\" }, [_vm._v(\"晴 24℃ 东南风\")]),\n      ]),\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"header-right\" }, [\n        _c(\"div\", { staticClass: \"header-buttons\" }, [\n          _vm._m(1),\n          _c(\n            \"div\",\n            { staticClass: \"header-btn home-btn\", on: { click: _vm.goHome } },\n            [_c(\"span\", [_vm._v(\"返回首页\")])]\n          ),\n        ]),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"screen-content\" }, [\n      _c(\"div\", { staticClass: \"left-panel\" }, [\n        _c(\"div\", { staticClass: \"performance-summary-section\" }, [\n          _vm._m(2),\n          _c(\"div\", { staticClass: \"performance-summary-content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"summary-grid\" },\n              _vm._l(_vm.performanceSummaryData, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"summary-item\" }, [\n                  _c(\"div\", { staticClass: \"summary-icon\" }, [\n                    _c(\"img\", { attrs: { src: item.icon, alt: item.name } }),\n                  ]),\n                  _c(\"div\", { staticClass: \"summary-info\" }, [\n                    _c(\"div\", { staticClass: \"summary-label\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                    _c(\"div\", { staticClass: \"summary-value\" }, [\n                      _vm._v(_vm._s(item.value)),\n                    ]),\n                  ]),\n                ])\n              }),\n              0\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"metting-section\" }, [\n          _vm._m(3),\n          _c(\"div\", { staticClass: \"metting-content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"meeting-grid\" },\n              _vm._l(_vm.meetingTypeData, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"meeting-item\" }, [\n                  _c(\"div\", { staticClass: \"meeting-number\" }, [\n                    _vm._v(_vm._s(item.value)),\n                  ]),\n                  _c(\"div\", { staticClass: \"meeting-label\" }, [\n                    _vm._v(_vm._s(item.name)),\n                  ]),\n                ])\n              }),\n              0\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"activity-section\" }, [\n          _vm._m(4),\n          _c(\n            \"div\",\n            { staticClass: \"activity-content\" },\n            [\n              _c(\"BarScrollChart\", {\n                attrs: {\n                  id: \"activityTypeChart\",\n                  showCount: 12,\n                  \"chart-data\": _vm.activityTypeData,\n                  \"alternate-colors\": true,\n                },\n              }),\n            ],\n            1\n          ),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"right-panel\" }, [\n        _c(\"div\", { staticClass: \"performance-analysis-section\" }, [\n          _vm._m(5),\n          _c(\"div\", { staticClass: \"performance-content\" }, [\n            _c(\"div\", { staticClass: \"table-container\" }, [\n              _vm._m(6),\n              _c(\n                \"div\",\n                { staticClass: \"table-body\" },\n                _vm._l(_vm.performanceData, function (item, index) {\n                  return _c(\"div\", { key: index, staticClass: \"table-row\" }, [\n                    _c(\"div\", { staticClass: \"table-cell name-col\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell meeting-col\" }, [\n                      _vm._v(_vm._s(item.meeting)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell proposal-col\" }, [\n                      _vm._v(_vm._s(item.proposal)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell opinion-col\" }, [\n                      _vm._v(_vm._s(item.opinion)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell suggestion-col\" }, [\n                      _vm._v(_vm._s(item.suggestion) + \" \"),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell reading-col\" }, [\n                      _vm._v(_vm._s(item.reading)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell training-col\" }, [\n                      _vm._v(_vm._s(item.training)),\n                    ]),\n                  ])\n                }),\n                0\n              ),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header-center\" }, [\n      _c(\"img\", {\n        staticStyle: { height: \"50px\" },\n        attrs: {\n          src: require(\"../../../assets/largeScreen/top_header_txt.png\"),\n          alt: \"\",\n        },\n      }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header-btn current-module-btn\" }, [\n      _c(\"span\", [_vm._v(\"履职统计\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"年度履职汇总\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"会议类型统计\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"活动类型统计\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"履职数据分析\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"table-header\" }, [\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"姓名\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"会议活动\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"政协提案\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"社情民意\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"议政建言\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"读书心得\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"委员培训\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEE,GAAG,EAAE,WAAP;IAAoBC,WAAW,EAAE;EAAjC,CAAR,EAAyD,CAChEH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CAC1CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAACO,WAAX,CAAP,CADuC,CAAvC,CADsC,EAIxCN,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAqC,CAACJ,GAAG,CAACK,EAAJ,CAAO,WAAP,CAAD,CAArC,CAJsC,CAAxC,CADwC,EAO1CL,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAP0C,EAQ1CP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD2C,EAE3CP,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE,qBAAf;IAAsCK,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACW;IAAb;EAA1C,CAFA,EAGA,CAACV,EAAE,CAAC,MAAD,EAAS,CAACD,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAT,CAAH,CAHA,CAFyC,CAA3C,CADuC,CAAzC,CARwC,CAA1C,CAD8D,EAoBhEJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwD,CACxDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADwD,EAExDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwD,CACxDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACa,sBAAX,EAAmC,UAAUC,IAAV,EAAgBC,KAAhB,EAAuB;IACxD,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAqD,CAC5DH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAyC,CACzCH,EAAE,CAAC,KAAD,EAAQ;MAAEgB,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,IAAZ;QAAkBC,GAAG,EAAEN,IAAI,CAACO;MAA5B;IAAT,CAAR,CADuC,CAAzC,CAD0D,EAI5DpB,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAyC,CACzCH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACO,IAAZ,CAAP,CAD0C,CAA1C,CADuC,EAIzCpB,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACQ,KAAZ,CAAP,CAD0C,CAA1C,CAJuC,CAAzC,CAJ0D,CAArD,CAAT;EAaD,CAdD,CAHA,EAkBA,CAlBA,CADsD,CAAxD,CAFsD,CAAxD,CADqC,EA0BvCrB,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD4C,EAE5CP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACuB,eAAX,EAA4B,UAAUT,IAAV,EAAgBC,KAAhB,EAAuB;IACjD,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAqD,CAC5DH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA2C,CAC3CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACQ,KAAZ,CAAP,CAD2C,CAA3C,CAD0D,EAI5DrB,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACO,IAAZ,CAAP,CAD0C,CAA1C,CAJ0D,CAArD,CAAT;EAQD,CATD,CAHA,EAaA,CAbA,CAD0C,CAA5C,CAF0C,CAA5C,CA1BqC,EA8CvCpB,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD6C,EAE7CP,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,gBAAD,EAAmB;IACnBgB,KAAK,EAAE;MACLO,EAAE,EAAE,mBADC;MAELC,SAAS,EAAE,EAFN;MAGL,cAAczB,GAAG,CAAC0B,gBAHb;MAIL,oBAAoB;IAJf;EADY,CAAnB,CADJ,CAHA,EAaA,CAbA,CAF2C,CAA7C,CA9CqC,CAAvC,CADyC,EAkE3CzB,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyD,CACzDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADyD,EAEzDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD4C,EAE5CP,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAAC2B,eAAX,EAA4B,UAAUb,IAAV,EAAgBC,KAAhB,EAAuB;IACjD,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAkD,CACzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAgD,CAChDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACO,IAAZ,CAAP,CADgD,CAAhD,CADuD,EAIzDpB,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACc,OAAZ,CAAP,CADmD,CAAnD,CAJuD,EAOzD3B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACe,QAAZ,CAAP,CADoD,CAApD,CAPuD,EAUzD5B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACgB,OAAZ,CAAP,CADmD,CAAnD,CAVuD,EAazD7B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsD,CACtDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACiB,UAAZ,IAA0B,GAAjC,CADsD,CAAtD,CAbuD,EAgBzD9B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACkB,OAAZ,CAAP,CADmD,CAAnD,CAhBuD,EAmBzD/B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACmB,QAAZ,CAAP,CADoD,CAApD,CAnBuD,CAAlD,CAAT;EAuBD,CAxBD,CAHA,EA4BA,CA5BA,CAF0C,CAA5C,CAD8C,CAAhD,CAFuD,CAAzD,CADsC,CAAxC,CAlEyC,CAA3C,CApB8D,CAAzD,CAAT;AAgID,CAnID;;AAoIA,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIlC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CACjDH,EAAE,CAAC,KAAD,EAAQ;IACRkC,WAAW,EAAE;MAAEC,MAAM,EAAE;IAAV,CADL;IAERnB,KAAK,EAAE;MACLC,GAAG,EAAEmB,OAAO,CAAC,gDAAD,CADP;MAELjB,GAAG,EAAE;IAFA;EAFC,CAAR,CAD+C,CAA1C,CAAT;AASD,CAbmB,EAcpB,YAAY;EACV,IAAIpB,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0D,CACjEH,EAAE,CAAC,MAAD,EAAS,CAACD,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAT,CAD+D,CAA1D,CAAT;AAGD,CApBmB,EAqBpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,QAAP,CAAD,CAA9C,CAD4C,CAAvC,CAAT;AAGD,CA3BmB,EA4BpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,QAAP,CAAD,CAA9C,CAD4C,CAAvC,CAAT;AAGD,CAlCmB,EAmCpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,QAAP,CAAD,CAA9C,CAD4C,CAAvC,CAAT;AAGD,CAzCmB,EA0CpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,QAAP,CAAD,CAA9C,CAD4C,CAAvC,CAAT;AAGD,CAhDmB,EAiDpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CAChDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAAxC,CAD8C,EAEhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAF8C,EAGhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAH8C,EAIhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAJ8C,EAKhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAL8C,EAMhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAN8C,EAOhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAP8C,EAQhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,CAR8C,CAAzC,CAAT;AAUD,CA9DmB,CAAtB;AAgEAL,MAAM,CAACuC,aAAP,GAAuB,IAAvB;AAEA,SAASvC,MAAT,EAAiBmC,eAAjB"}]}