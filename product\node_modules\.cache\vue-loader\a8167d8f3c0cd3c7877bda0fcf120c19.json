{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755766285960}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BarScrollChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarScrollChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    },\r\n    alternateColors: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor (index = 0) {\r\n      if (this.alternateColors) {\r\n        // 交替颜色：蓝色和黄色\r\n        if (index % 2 === 0) {\r\n          // 蓝色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#062553' },\r\n              { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n            ]\r\n          }\r\n        } else {\r\n          // 黄色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#072756' },\r\n              { offset: 1, color: 'rgba(245, 231, 79, 1)' }\r\n            ]\r\n          }\r\n        }\r\n      } else {\r\n        // 默认蓝色渐变\r\n        return {\r\n          type: 'linear',\r\n          x: 0,\r\n          y: 0,\r\n          x2: 1,\r\n          y2: 0,\r\n          colorStops: [\r\n            { offset: 0, color: '#062553' },\r\n            { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'none'\r\n          },\r\n          backgroundColor: 'rgba(0, 20, 40, 0.9)',\r\n          borderColor: 'rgba(31, 198, 255, 0.8)',\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: '#FFFFFF',\r\n            fontSize: 14\r\n          },\r\n          formatter: (params) => {\r\n            if (params && params.length > 0) {\r\n              const data = params[0]\r\n              return `\r\n                <div style=\"padding: 8px;\">\r\n                  <div style=\"color: #1FC6FF; font-weight: bold; margin-bottom: 4px;\">\r\n                    ${data.name}\r\n                  </div>\r\n                  <div style=\"color: #FFFFFF;\">\r\n                    数量: <span style=\"color: #F5E74F; font-weight: bold;\">${data.value}</span>\r\n                  </div>\r\n                </div>\r\n              `\r\n            }\r\n            return ''\r\n          }\r\n        },\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 15 : this.id === 'activityTypeChart' ? 45 : 35,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              align: 'right',\r\n              margin: 16,\r\n              formatter: (value, idx) => {\r\n                if (this.id === 'activityTypeChart') {\r\n                  return `{name|${value}}`\r\n                } else {\r\n                  const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n                  return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`\r\n                }\r\n              },\r\n              rich: {\r\n                num: {\r\n                  color: 'rgba(255, 255, 255, 0.5)',\r\n                  fontSize: 13,\r\n                  fontFamily: 'DIN',\r\n                  fontWeight: '500',\r\n                  align: 'left',\r\n                  // width: 25,\r\n                  padding: [4, 0, 0, 0]\r\n                },\r\n                name: {\r\n                  color: '#fff',\r\n                  fontSize: 15,\r\n                  padding: [0, 0, 0, 4]\r\n                }\r\n              }\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 0,\r\n            data: data.map((item, index) => ({\r\n              value: item.value,\r\n              name: item.name,\r\n              itemStyle: {\r\n                color: this.getBarColor(this.alternateColors ? (this.currentIndex + index) : 0),\r\n                borderRadius: 6\r\n              }\r\n            })),\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 0,\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}