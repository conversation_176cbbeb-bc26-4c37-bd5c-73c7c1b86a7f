{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755766167884}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BarScrollChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarScrollChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    },\r\n    alternateColors: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor (index = 0) {\r\n      if (this.alternateColors) {\r\n        // 交替颜色：蓝色和黄色\r\n        if (index % 2 === 0) {\r\n          // 蓝色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#062553' },\r\n              { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n            ]\r\n          }\r\n        } else {\r\n          // 黄色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#072756' },\r\n              { offset: 1, color: 'rgba(245, 231, 79, 1)' }\r\n            ]\r\n          }\r\n        }\r\n      } else {\r\n        // 默认蓝色渐变\r\n        return {\r\n          type: 'linear',\r\n          x: 0,\r\n          y: 0,\r\n          x2: 1,\r\n          y2: 0,\r\n          colorStops: [\r\n            { offset: 0, color: '#062553' },\r\n            { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 15 : this.id === 'activityTypeChart' ? 45 : 35,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              align: 'right',\r\n              margin: 16,\r\n              formatter: (value, idx) => {\r\n                if (this.id === 'activityTypeChart') {\r\n                  return `{name|${value}}`\r\n                } else {\r\n                  const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n                  return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`\r\n                }\r\n              },\r\n              rich: {\r\n                num: {\r\n                  color: 'rgba(255, 255, 255, 0.5)',\r\n                  fontSize: 13,\r\n                  fontFamily: 'DIN',\r\n                  fontWeight: '500',\r\n                  align: 'left',\r\n                  // width: 25,\r\n                  padding: [4, 0, 0, 0]\r\n                },\r\n                name: {\r\n                  color: '#fff',\r\n                  fontSize: 15,\r\n                  padding: [0, 0, 0, 4]\r\n                }\r\n              }\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 0,\r\n            data: data.map((item, index) => ({\r\n              value: item.value,\r\n              itemStyle: {\r\n                color: this.getBarColor(this.alternateColors ? (this.currentIndex + index) : 0),\r\n                borderRadius: 6\r\n              }\r\n            })),\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 0,\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}