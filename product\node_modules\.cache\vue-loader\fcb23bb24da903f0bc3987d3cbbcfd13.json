{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=template&id=76285fab&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1755761451414}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}