<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right">
        <div class="header-buttons">
          <div class="header-btn current-module-btn">
            <span>履职统计</span>
          </div>
          <div class="header-btn home-btn" @click="goHome">
            <span>返回首页</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 年度履职汇总 -->
        <div class="performance-summary-section">
          <div class="header_box">
            <span class="header_text_left">年度履职汇总</span>
          </div>
          <div class="performance-summary-content">
            <div class="summary-grid">
              <div class="summary-item" v-for="(item, index) in performanceSummaryData" :key="index">
                <div class="summary-icon">
                  <img :src="item.icon" :alt="item.name" />
                </div>
                <div class="summary-info">
                  <div class="summary-label">{{ item.name }}</div>
                  <div class="summary-value">{{ item.value }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 会议类型统计 -->
        <div class="metting-section">
          <div class="header_box">
            <span class="header_text_left">会议类型统计</span>
          </div>
          <div class="metting-content">
            <div class="meeting-grid">
              <div class="meeting-item" v-for="(item, index) in meetingTypeData" :key="index">
                <div class="meeting-number">{{ item.value }}</div>
                <div class="meeting-label">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 活动类型统计 -->
        <div class="activity-section">
          <div class="header_box">
            <span class="header_text_left">活动类型统计</span>
          </div>
          <div class="activity-content">
            <BarScrollChart id="activityTypeChart" :showCount="12" :chart-data="activityTypeData"
              :alternate-colors="true" />
          </div>
        </div>
      </div>

      <div class="right-panel">
        <!-- 履职数据分析 -->
        <div class="performance-analysis-section">
          <div class="header_box">
            <span class="header_text_left">履职数据分析</span>
          </div>
          <div class="performance-content">
            <div class="table-container">
              <!-- 固定表头 -->
              <div class="table-header">
                <div class="header-cell">姓名</div>
                <div class="header-cell">会议活动</div>
                <div class="header-cell">政协提案</div>
                <div class="header-cell">社情民意</div>
                <div class="header-cell">议政建言</div>
                <div class="header-cell">读书心得</div>
                <div class="header-cell">委员培训</div>
                <div class="header-cell"></div> <!-- 滚动条占位 -->
              </div>
              <!-- 可滚动内容 -->
              <div class="table-body">
                <div class="table-row" v-for="(item, index) in performanceData" :key="index">
                  <div class="table-cell name-col">{{ item.name }}</div>
                  <div class="table-cell meeting-col">{{ item.meeting }}</div>
                  <div class="table-cell proposal-col">{{ item.proposal }}</div>
                  <div class="table-cell opinion-col">{{ item.opinion }}</div>
                  <div class="table-cell suggestion-col">{{ item.suggestion }}
                  </div>
                  <div class="table-cell reading-col">{{ item.reading }}</div>
                  <div class="table-cell training-col">{{ item.training }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import BarScrollChart from '../components/BarScrollChart.vue'

export default {
  name: 'BigScreen',
  components: {
    BarScrollChart
  },
  data () {
    return {
      currentTime: '',
      // 履职数据分析表格数据
      performanceData: [
        { name: '马家', meeting: 15, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '马家', meeting: 0, proposal: 12, opinion: 0, suggestion: 0, reading: 15, training: 0 },
        { name: '主席团', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },
        { name: '主席团', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 60, training: 0 },
        { name: '主席', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },
        { name: '刘振南', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },
        { name: '刘军', meeting: 20, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '吴学宁', meeting: 15, proposal: 38, opinion: 0, suggestion: 0, reading: 55, training: 0 },
        { name: '杨文军', meeting: 60, proposal: 28, opinion: 0, suggestion: 0, reading: 55, training: 0 },
        { name: '苏勇', meeting: 9, proposal: 13, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '杨海', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },
        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },
        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },
        { name: '领作', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 60, training: 0 },
        { name: '海勇员', meeting: 20, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '周明', meeting: 15, proposal: 38, opinion: 0, suggestion: 0, reading: 55, training: 0 },
        { name: '刘峰', meeting: 15, proposal: 0, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '吴芳梅', meeting: 0, proposal: 12, opinion: 0, suggestion: 0, reading: 15, training: 0 },
        { name: '朱敏', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },
        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },
        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 },
        { name: '苏勇', meeting: 9, proposal: 13, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '杨海', meeting: 0, proposal: 2, opinion: 0, suggestion: 0, reading: 15, training: 0 },
        { name: '杨海', meeting: 0, proposal: 1, opinion: 4, suggestion: 4, reading: 15, training: 0 },
        { name: '王路', meeting: 15, proposal: 0, opinion: 2, suggestion: 2, reading: 25, training: 0 }
      ],
      // 活动类型统计数据
      activityTypeData: [
        { name: '视察', value: 32 },
        { name: '调研', value: 20 },
        { name: '协商', value: 14 },
        { name: '学习培训', value: 22 },
        { name: '联系界别群众', value: 8 },
        { name: '提案督查', value: 26 },
        { name: '提案督办', value: 13 },
        { name: '提案评议', value: 32 },
        { name: '委员联络小组', value: 15 },
        { name: '委员会客厅', value: 25 },
        { name: '联系社会组织', value: 10 },
        { name: '界别群众重点关切问题情况通报会', value: 20 },
        { name: '社情民意座谈会', value: 16 },
        { name: '接受媒体采访', value: 28 },
        { name: '经济协作参加各委会议活动', value: 5 },
        { name: '宣讲党的政策', value: 7 },
        { name: '服务为民', value: 32 }
      ],
      // 会议类型统计数据
      meetingTypeData: [
        { name: '全体会议', value: 2 },
        { name: '常委会议', value: 10 },
        { name: '主席会议', value: 12 },
        { name: '其他会议', value: 28 }
      ],
      // 年度履职汇总数据
      performanceSummaryData: [
        { name: '提交提案', value: 1500, icon: require('../../../assets/largeScreen/icon_submit_proposal.png') },
        { name: '提交社情民意', value: 1057, icon: require('../../../assets/largeScreen/icon_submit_social.png') },
        { name: '网络议政', value: 215, icon: require('../../../assets/largeScreen/icon_network.png') },
        { name: '参加会议', value: 361, icon: require('../../../assets/largeScreen/icon_metting_item.png') },
        { name: '参加活动', value: 104, icon: require('../../../assets/largeScreen/icon_activity_item.png') },
        { name: '其他履职', value: 241, icon: require('../../../assets/largeScreen/item_other_duties.png') }
      ]
    }
  },
  computed: {
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 返回首页
    goHome () {
      this.$router.push({ path: '/homeBox' })
    },
    // 处理树节点点击
    handleNodeClick (data, node) {
      // 允许选择所有节点（包括父级青岛）
      this.selectedArea = data.name
      this.selectedDistrictCode = data.code
      this.showAreaPopover = false
      // 这里可以添加切换地区后的数据更新逻辑
      console.log('选择了地区:', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-buttons {
        display: flex;
        gap: 15px;

        .header-btn {
          height: 42px;
          line-height: 42px;
          padding: 0 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);
            border-color: rgba(0, 181, 254, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &.current-module-btn {
            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: bold;
            font-size: 16px;
            color: #FFFFFF;
          }

          &.home-btn {
            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: 400;
            font-size: 16px;
            color: #1FC6FF;
          }

          &.area-select-btn {
            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);
            border: 1px solid rgba(0, 181, 254, 0.5);
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            color: #1FC6FF;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 120px;

            .dropdown-icon {
              margin-left: 8px;
              font-size: 12px;
              transition: transform 0.3s ease;
              color: #1FC6FF;

              &.active {
                transform: rotate(180deg);
              }
            }

            &:hover {
              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);
              border-color: rgba(0, 181, 254, 0.8);
            }
          }
        }
      }
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 20px;
    gap: 20px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 20px;
      height: 100%;
    }

    .right-panel {
      width: 922px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    // 左侧面板样式
    .left-panel {

      // 年度履职汇总
      .performance-summary-section {
        height: 390px;
        background: url('../../../assets/largeScreen/icon_performance_summary_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1; // 第一列
        grid-row: 1; // 第一行

        .performance-summary-content {
          margin-top: 50px;
          height: calc(100% - 50px);

          .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr 1fr;
            gap: 12px;
            height: 100%;

            .summary-item {
              display: flex;
              align-items: center;

              .summary-icon {
                width: 64px;
                height: 64px;
                margin-right: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 6px;

                img {
                  width: 64px;
                  height: 64px;
                  object-fit: contain;
                }
              }

              .summary-info {
                flex: 1;

                .summary-label {
                  font-weight: 400;
                  font-size: 14px;
                  color: #B4C0CC;
                  margin-bottom: 8px;
                  line-height: 20px;
                }

                .summary-value {
                  font-size: 18px;
                  font-weight: bold;
                  color: #1FC6FF;
                  font-family: 'DIN', Arial, sans-serif;
                  text-shadow: 0 0 6px rgba(31, 198, 255, 0.3);
                }
              }
            }
          }
        }
      }

      // 会议类型统计
      .metting-section {
        height: 390px;
        background: url('../../../assets/largeScreen/icon_metting_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 2; // 第二列
        grid-row: 1; // 第一行

        .metting-content {
          margin-top: 60px;
          height: calc(100% - 70px);

          .meeting-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 15px;
            height: 100%;

            .meeting-item {
              width: 200px;
              height: 125px;
              background: url('../../../assets/largeScreen/icon_metting_type.png') no-repeat;
              background-size: 100% 100%;
              background-position: center;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              padding: 15px;
              cursor: pointer;

              .meeting-number {
                font-weight: 500;
                font-size: 32px;
                color: #FFFFFF;
                margin-bottom: 10px;
                font-family: 'DIN', Arial, sans-serif;
                transition: all 0.3s ease;
              }

              .meeting-label {
                font-size: 16px;
                color: #FFFFFF;
                font-weight: 500;
                text-align: center;
                line-height: 1.2;
              }
            }
          }
        }
      }

      // 活动类型统计
      .activity-section {
        height: 550px;
        background: url('../../../assets/largeScreen/icon_activity_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1 / -1; // 跨越两列（活动类型统计在第二行）
        grid-row: 2; // 第二行

        .activity-content {
          margin-top: 30px;
          height: calc(100% - 30px);
        }
      }
    }

    .right-panel {

      // 履职数据分析
      .performance-analysis-section {
        background: url('../../../assets/largeScreen/icon_performance_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        height: 100%;

        .performance-content {
          margin-top: 50px;
          height: calc(100% - 50px);

          .table-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid #117090;
            overflow: hidden;
            --name-col-width: 124px;
            --scrollbar-width: 6px;

            .table-header {
              display: grid;
              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);
              border-bottom: 1px solid #117090;
              position: sticky;
              top: 0;
              z-index: 10;

              .header-cell {
                height: 40px;
                line-height: 40px;
                text-align: center;
                font-weight: 400;
                color: #B4C0CC;
                font-size: 15px;
                border-right: 1px solid #117090;
                display: flex;
                align-items: center;
                justify-content: center;

                &:last-child {
                  border-right: none;
                  background: transparent;
                  border: none;
                }

                // &.name-col {
                //   background: rgba(0, 100, 180, 0.9);
                //   font-weight: 600;
                // }
              }
            }

            .table-body {
              flex: 1;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: rgba(0, 30, 60, 0.3);
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(0, 212, 255, 0.4);
                border-radius: 3px;

                &:hover {
                  background: rgba(0, 212, 255, 0.6);
                }
              }

              .table-row {
                display: grid;
                grid-template-columns: var(--name-col-width) repeat(6, 1fr);
                border-bottom: 1px solid #117090;
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(0, 212, 255, 0.1);
                }

                .table-cell {
                  padding: 12px 8px;
                  text-align: center;
                  color: #FFFFFF;
                  font-size: 14px;
                  border-right: 1px solid #117090;
                  transition: all 0.3s ease;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: rgba(31, 198, 255, 0.16);

                  &:last-child {
                    border-right: none;
                  }

                  &.name-col {
                    background: rgba(31, 198, 255, 0.16);
                    color: #FFF;
                    font-weight: 500;
                  }

                  &.meeting-col {
                    // background: rgba(10, 63, 111, 0.4);
                    color: #59F7CA;
                    font-weight: 500;
                    font-size: 16px;
                  }

                  &.proposal-col {
                    // background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #00FFF7;
                  }

                  &.opinion-col {
                    // background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #FF386B;
                  }

                  &.suggestion-col {
                    // background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #81C4E4;
                  }

                  &.reading-col {
                    // background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #387BFD;
                  }

                  &.training-col {
                    // background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #FF911F;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
